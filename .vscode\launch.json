{"configurations": [{"type": "java", "name": "ApiTokenServiceApplication", "request": "launch", "mainClass": "com.vance.demo.ApiTokenServiceApplication", "projectName": "api-token-service"}, {"type": "java", "name": "Spring Boot-ApiTokenServiceApplication<api-token-service>", "request": "launch", "cwd": "${workspaceFolder}", "mainClass": "com.vance.api_token_service.ApiTokenServiceApplication", "projectName": "api-token-service", "args": "", "envFile": "${workspaceFolder}/.env"}]}