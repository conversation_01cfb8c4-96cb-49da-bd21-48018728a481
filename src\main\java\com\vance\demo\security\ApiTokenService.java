package com.vance.demo.security;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.SecureRandom;
import java.time.Instant;
import java.util.Base64;
import java.util.List;

import org.springframework.stereotype.Service;

import com.vance.demo.entity.ApiToken;
import com.vance.demo.repository.ApiTokenRepository;

/**
 * ApiTokenService 負責 Token 的生成、驗證與撤銷。
 */
@Service
public class ApiTokenService {

    private final ApiTokenRepository repository;
    private static final SecureRandom random = new SecureRandom();

    public ApiTokenService(ApiTokenRepository repository) {
        this.repository = repository;
    }

    /**
     * 產生新的 API Token（僅顯示一次明文）
     * 
     * @param userId     使用者 ID
     * @param name       Token 名稱
     * @param ttlSeconds 有效期限秒數
     * @return Token 明文字串
     */
    public String createToken(String userId, String name, long ttlSeconds) {
        byte[] bytes = new byte[32];
        random.nextBytes(bytes);
        String plain = "sk-" + Base64.getUrlEncoder().withoutPadding().encodeToString(bytes);
        String hash = sha256(plain);

        ApiToken token = new ApiToken();
        token.setUserId(userId);
        token.setTokenHash(hash);
        token.setName(name);
        token.setCreatedAt(Instant.now());
        token.setExpiredAt(Instant.now().plusSeconds(ttlSeconds));
        token.setRevoked(false);
        repository.save(token);

        return plain;
    }

    /**
     * 根據 Token 明文驗證是否有效。
     * 
     * @param plainToken 請求中的明文 Token
     * @return 驗證結果 true/false
     */
    public boolean verifyToken(String plainToken) {
        String hash = sha256(plainToken);
        return repository.findByTokenHash(hash)
                .filter(t -> !t.isRevoked())
                .filter(t -> t.getExpiredAt() == null || t.getExpiredAt().isAfter(Instant.now()))
                .isPresent();
    }

    /**
     * 將指定 Token 設為撤銷狀態。
     * 
     * @param id Token 主鍵 ID
     */
    public void revokeToken(Long id) {
        repository.findById(id).ifPresent(t -> {
            t.setRevoked(true);
            repository.save(t);
        });
    }

    /**
     * 查詢指定使用者的所有 Token
     */
    public List<ApiToken> listTokens(String userId) {
        return repository.findAll().stream()
                .filter(t -> t.getUserId().equals(userId))
                .toList();
    }

    /** 計算 SHA-256 雜湊 */
    private String sha256(String value) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hashed = digest.digest(value.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(hashed);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
