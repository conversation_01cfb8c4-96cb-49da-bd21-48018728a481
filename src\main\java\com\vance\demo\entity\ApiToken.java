package com.vance.demo.entity;

import java.time.Instant;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

/**
 * ApiToken 實體類別，對應資料庫中的 token 資料表。
 * 用於儲存 API Token 的雜湊、屬性與狀態。
 */
@Entity
@Table(name = "api_tokens")
public class ApiToken {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /** 所屬使用者 ID（例如開發者帳號） */
    @Column(nullable = false)
    private String userId;

    /** Token 的 SHA-256 雜湊字串 */
    @Column(nullable = false, unique = true, length = 88)
    private String tokenHash;

    /** Token 的顯示名稱（例如 "Main API Key"） */
    private String name;

    /** 建立時間 */
    @Column(nullable = false)
    private Instant createdAt;

    /** 過期時間 */
    private Instant expiredAt;

    /** 是否被撤銷 */
    @Column(nullable = false)
    private boolean revoked;

    /** 最近使用時間 */
    private Instant lastUsedAt;

    // Getter / Setter 區
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getTokenHash() {
        return tokenHash;
    }

    public void setTokenHash(String tokenHash) {
        this.tokenHash = tokenHash;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Instant getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public Instant getExpiredAt() {
        return expiredAt;
    }

    public void setExpiredAt(Instant expiredAt) {
        this.expiredAt = expiredAt;
    }

    public boolean isRevoked() {
        return revoked;
    }

    public void setRevoked(boolean revoked) {
        this.revoked = revoked;
    }

    public Instant getLastUsedAt() {
        return lastUsedAt;
    }

    public void setLastUsedAt(Instant lastUsedAt) {
        this.lastUsedAt = lastUsedAt;
    }
}
