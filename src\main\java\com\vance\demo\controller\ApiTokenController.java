package com.vance.demo.controller;

import java.util.List;
import java.util.Map;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.vance.demo.entity.ApiToken;
import com.vance.demo.security.ApiTokenService;

/**
 * ApiTokenController 提供 Token 相關 REST API。
 */
@RestController
@RequestMapping("/api/tokens")
public class ApiTokenController {

    private final ApiTokenService tokenService;

    public ApiTokenController(ApiTokenService tokenService) {
        this.tokenService = tokenService;
    }

    /**
     * 建立新的 API Token
     * 
     * @param request 包含 userId 與 name
     * @return 回傳明文 Token
     */
    @PostMapping
    public Map<String, String> createToken(@RequestBody Map<String, Object> request) {
        String userId = (String) request.get("userId");
        String name = (String) request.getOrDefault("name", "default");
        long ttl = ((Number) request.getOrDefault("ttl", 86400)).longValue(); // 預設 1 天
        String token = tokenService.createToken(userId, name, ttl);
        return Map.of("token", token);
    }

    /**
     * 查詢使用者的所有 Token（不含明文）
     */
    @GetMapping
    public List<ApiToken> listTokens(@RequestParam String userId) {
        return tokenService.listTokens(userId);
    }

    /**
     * 撤銷指定 Token
     */
    @DeleteMapping("/{id}")
    public void revokeToken(@PathVariable Long id) {
        tokenService.revokeToken(id);
    }
}
