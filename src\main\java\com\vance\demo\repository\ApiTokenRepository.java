package com.vance.demo.repository;

import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;

import com.vance.demo.entity.ApiToken;

/**
 * ApiTokenRepository 用於操作 ApiToken 資料表。
 */
public interface ApiTokenRepository extends JpaRepository<ApiToken, Long> {

    /**
     * 根據 tokenHash 查詢 Token
     * 
     * @param tokenHash 雜湊後的 Token
     * @return Optional<ApiToken>
     */
    Optional<ApiToken> findByTokenHash(String tokenHash);
}
